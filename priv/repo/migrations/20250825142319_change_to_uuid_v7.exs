defmodule Qx.Repo.Migrations.ChangeToUuidV7 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:users) do
      modify :id, :uuid, default: fragment("uuid_generate_v7()")
    end
  end

  def down do
    alter table(:users) do
      modify :id, :uuid, default: fragment("gen_random_uuid()")
    end
  end
end
