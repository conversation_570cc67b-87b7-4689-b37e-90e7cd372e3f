defmodule Qx.Repo.Migrations.AddAccounts do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:users) do
      add :account_id, :uuid, null: false
    end

    create table(:accounts, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
    end

    alter table(:users) do
      modify :account_id,
             references(:accounts,
               column: :id,
               name: "users_account_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:users, [:account_id], name: "users_by_account_index")

    alter table(:accounts) do
      add :name, :citext, null: false
      add :description, :text
      add :status, :text, default: "active"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end
  end

  def down do
    alter table(:accounts) do
      remove :updated_at
      remove :inserted_at
      remove :status
      remove :description
      remove :name
    end

    drop_if_exists unique_index(:users, [:account_id], name: "users_by_account_index")

    drop constraint(:users, "users_account_id_fkey")

    alter table(:users) do
      modify :account_id, :uuid
    end

    drop table(:accounts)

    alter table(:users) do
      remove :account_id
    end
  end
end
