{"attributes": [{"allow_nil?": false, "default": "fragment(\"uuid_generate_v7()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "email", "type": "citext"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "users_account_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "accounts"}, "scale": null, "size": null, "source": "account_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "BF0019591D8A9ADE66B770B7AC120A949E4F0148B2F888E18A46514527274F68", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "users_unique_email_index", "keys": [{"type": "atom", "value": "email"}], "name": "unique_email", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "users_by_account_index", "keys": [{"type": "atom", "value": "account_id"}], "name": "by_account", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Qx.Repo", "schema": null, "table": "users"}