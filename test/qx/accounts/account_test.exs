defmodule Qx.Accounts.AccountTest do
  use Qx.DataCase, async: true

  alias Qx.Accounts.Account

  describe "Account creation" do
    test "creates account with valid attributes" do
      attrs = %{
        name: "test company",
        description: "A test company",
        status: :active
      }

      assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)
      # Name should be normalized
      assert account.name.string == "TEST-COMPANY"
      assert account.description == "A test company"
      assert account.status == :active
      assert account.id != nil
      assert account.inserted_at != nil
      assert account.updated_at != nil
    end

    test "creates account with minimal required attributes" do
      attrs = %{name: "minimal company"}

      assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)
      assert account.name.string == "MINIMAL-COMPANY"
      assert account.description == nil
      # Default status
      assert account.status == :active
    end

    test "creates account with different status values" do
      for status <- [:active, :inactive, :suspended] do
        attrs = %{name: "company #{status}", status: status}
        assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)
        assert account.status == status
      end
    end
  end

  describe "Account validation failures" do
    test "fails to create account without name" do
      attrs = %{description: "No name company"}

      assert {:error, error} = Ash.create(Account, attrs, domain: Qx.Accounts)
      assert %Ash.Error.Invalid{} = error
    end

    test "fails to create account with nil name" do
      attrs = %{name: nil, description: "Nil name company"}

      assert {:error, error} = Ash.create(Account, attrs, domain: Qx.Accounts)
      assert %Ash.Error.Invalid{} = error
    end

    test "fails to create account with empty name" do
      attrs = %{name: "", description: "Empty name company"}

      assert {:error, error} = Ash.create(Account, attrs, domain: Qx.Accounts)
      assert %Ash.Error.Invalid{} = error
    end

    test "fails to create account with invalid status" do
      attrs = %{name: "Invalid Status Company", status: :invalid_status}

      assert {:error, error} = Ash.create(Account, attrs, domain: Qx.Accounts)
      assert %Ash.Error.Invalid{} = error
    end
  end

  describe "Account name normalization" do
    test "automatically normalizes name (trim, spaces to dashes, uppercase)" do
      test_cases = [
        {"lowercase", "LOWERCASE"},
        {"MixedCase", "MIXEDCASE"},
        {"UPPERCASE", "UPPERCASE"},
        {"with spaces", "WITH-SPACES"},
        {"with-dashes", "WITH-DASHES"},
        {"with_underscores", "WITH_UNDERSCORES"},
        {"with123numbers", "WITH123NUMBERS"},
        {"  leading spaces", "LEADING-SPACES"},
        {"trailing spaces  ", "TRAILING-SPACES"},
        {"  both sides  ", "BOTH-SIDES"},
        {"multiple   spaces", "MULTIPLE-SPACES"},
        {"mixed   spaces and-dashes", "MIXED-SPACES-AND-DASHES"},
        {"tabs\tand\tnewlines\n", "TABS-AND-NEWLINES"}
      ]

      for {input, expected} <- test_cases do
        attrs = %{name: input}
        assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)

        assert account.name.string == expected,
               "Expected '#{input}' to become '#{expected}', got '#{account.name.string}'"
      end
    end

    test "handles empty and whitespace-only names" do
      # These should fail validation since name is required and non-nil
      test_cases = ["", "   ", "\t\n  "]

      for input <- test_cases do
        attrs = %{name: input}
        assert {:error, _error} = Ash.create(Account, attrs, domain: Qx.Accounts)
      end
    end
  end

  describe "Account attribute defaults and constraints" do
    test "sets default status to active" do
      attrs = %{name: "Default Status Company"}

      assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)
      assert account.status == :active
    end

    test "allows nil description" do
      attrs = %{name: "No Description Company", description: nil}

      assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)
      assert account.description == nil
    end

    test "allows empty string description" do
      attrs = %{name: "Empty Description Company", description: ""}

      assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)
      # Empty strings are converted to nil in Ash by default
      assert account.description == nil
    end

    test "generates uuid_v7 primary key" do
      attrs = %{name: "UUID Test Company"}

      assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)
      assert is_binary(account.id)
      # UUID format
      assert String.length(account.id) == 36
      assert String.contains?(account.id, "-")
    end

    test "sets timestamps on creation" do
      attrs = %{name: "Timestamp Test Company"}

      before_creation = DateTime.utc_now()
      assert {:ok, account} = Ash.create(Account, attrs, domain: Qx.Accounts)
      after_creation = DateTime.utc_now()

      assert account.inserted_at != nil
      assert account.updated_at != nil
      assert DateTime.compare(account.inserted_at, before_creation) in [:gt, :eq]
      assert DateTime.compare(account.inserted_at, after_creation) in [:lt, :eq]
      assert DateTime.compare(account.updated_at, before_creation) in [:gt, :eq]
      assert DateTime.compare(account.updated_at, after_creation) in [:lt, :eq]
    end
  end

  describe "Account custom actions" do
    setup do
      {:ok, active_account} =
        Ash.create(Account, %{name: "Active Company", status: :active}, domain: Qx.Accounts)

      {:ok, inactive_account} =
        Ash.create(Account, %{name: "Inactive Company", status: :inactive}, domain: Qx.Accounts)

      {:ok, suspended_account} =
        Ash.create(Account, %{name: "Suspended Company", status: :suspended}, domain: Qx.Accounts)

      %{
        active_account: active_account,
        inactive_account: inactive_account,
        suspended_account: suspended_account
      }
    end

    test "list_active action returns only active accounts", %{
      active_account: active_account,
      inactive_account: _inactive_account,
      suspended_account: _suspended_account
    } do
      active_accounts = Ash.read!(Account, action: :list_active, domain: Qx.Accounts)

      account_ids = Enum.map(active_accounts, & &1.id)
      assert active_account.id in account_ids

      # Verify only active accounts are returned
      for account <- active_accounts do
        assert account.status == :active
      end
    end

    test "activate action changes status to active", %{inactive_account: inactive_account} do
      assert inactive_account.status == :inactive

      assert {:ok, updated_account} =
               Ash.update(inactive_account, %{}, action: :activate, domain: Qx.Accounts)

      assert updated_account.status == :active
      assert updated_account.id == inactive_account.id
    end

    test "activate action works on suspended accounts", %{suspended_account: suspended_account} do
      assert suspended_account.status == :suspended

      assert {:ok, updated_account} =
               Ash.update(suspended_account, %{}, action: :activate, domain: Qx.Accounts)

      assert updated_account.status == :active
    end

    test "deactivate action changes status to inactive", %{active_account: active_account} do
      assert active_account.status == :active

      assert {:ok, updated_account} =
               Ash.update(active_account, %{}, action: :deactivate, domain: Qx.Accounts)

      assert updated_account.status == :inactive
      assert updated_account.id == active_account.id
    end

    test "deactivate action works on suspended accounts", %{suspended_account: suspended_account} do
      assert suspended_account.status == :suspended

      assert {:ok, updated_account} =
               Ash.update(suspended_account, %{}, action: :deactivate, domain: Qx.Accounts)

      assert updated_account.status == :inactive
    end
  end

  describe "Account standard CRUD actions" do
    test "read action retrieves account by id" do
      {:ok, account} = Ash.create(Account, %{name: "Read Test Company"}, domain: Qx.Accounts)

      assert {:ok, retrieved_account} = Ash.get(Account, account.id, domain: Qx.Accounts)
      assert retrieved_account.id == account.id
      assert retrieved_account.name.string == account.name.string
    end

    test "read action returns all accounts" do
      {:ok, _account1} = Ash.create(Account, %{name: "Company One"}, domain: Qx.Accounts)
      {:ok, _account2} = Ash.create(Account, %{name: "Company Two"}, domain: Qx.Accounts)

      accounts = Ash.read!(Account, domain: Qx.Accounts)
      assert length(accounts) >= 2
    end

    test "update action modifies account attributes" do
      {:ok, account} =
        Ash.create(Account, %{name: "Original Name", description: "Original description"},
          domain: Qx.Accounts
        )

      update_attrs = %{
        name: "updated name",
        description: "Updated description",
        status: :suspended
      }

      assert {:ok, updated_account} = Ash.update(account, update_attrs, domain: Qx.Accounts)
      # Should be normalized
      assert updated_account.name.string == "UPDATED-NAME"
      assert updated_account.description == "Updated description"
      assert updated_account.status == :suspended
      assert updated_account.id == account.id
    end

    test "destroy action deletes account" do
      {:ok, account} = Ash.create(Account, %{name: "To Be Deleted"}, domain: Qx.Accounts)

      assert :ok = Ash.destroy(account, domain: Qx.Accounts)
      assert {:error, %Ash.Error.Invalid{}} = Ash.get(Account, account.id, domain: Qx.Accounts)
    end
  end

  describe "Account case-insensitive name handling" do
    test "name attribute uses case-insensitive string type" do
      {:ok, account1} = Ash.create(Account, %{name: "test company"}, domain: Qx.Accounts)
      {:ok, account2} = Ash.create(Account, %{name: "TEST COMPANY"}, domain: Qx.Accounts)

      # Both should be stored as normalized due to the change
      assert account1.name.string == "TEST-COMPANY"
      assert account2.name.string == "TEST-COMPANY"

      # Both accounts should exist as separate entities
      assert account1.id != account2.id
    end
  end
end
