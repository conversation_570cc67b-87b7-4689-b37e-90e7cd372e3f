defmodule Qx.Accounts.Changes.NormalizeNameTest do
  use Qx.DataCase, async: true

  alias Qx.Accounts.Changes.NormalizeName
  alias Qx.Accounts.Account

  describe "NormalizeName.change/3" do
    test "normalizes regular string names" do
      changeset = Ash.Changeset.for_create(Account, :create, %{name: "  test company  "})

      result = NormalizeName.change(changeset, [], %{})

      name_value = Ash.Changeset.get_attribute(result, :name)
      assert name_value.string == "TEST-COMPANY"
    end

    test "normalizes CiString names" do
      changeset = Ash.Changeset.for_create(Account, :create, %{name: "  mixed Case Company  "})

      result = NormalizeName.change(changeset, [], %{})

      name_value = Ash.Changeset.get_attribute(result, :name)
      assert name_value.string == "MIXED-CASE-COMPANY"
    end

    test "handles nil names" do
      changeset = Ash.Changeset.for_create(Account, :create, %{})

      result = NormalizeName.change(changeset, [], %{})

      # Should remain unchanged since name is nil
      assert Ash.Changeset.get_attribute(result, :name) == nil
    end

    test "normalizes various whitespace patterns" do
      test_cases = [
        {"simple", "SIMPLE"},
        {"with spaces", "WITH-SPACES"},
        {"  leading spaces", "LEADING-SPACES"},
        {"trailing spaces  ", "TRAILING-SPACES"},
        {"  both sides  ", "BOTH-SIDES"},
        {"multiple   spaces", "MULTIPLE-SPACES"},
        {"tabs\tand\tnewlines\n", "TABS-AND-NEWLINES"},
        {"mixed\t  \nwhitespace", "MIXED-WHITESPACE"},
        {"already-dashed", "ALREADY-DASHED"},
        {"mixed   spaces and-dashes", "MIXED-SPACES-AND-DASHES"}
      ]

      for {input, expected} <- test_cases do
        changeset = Ash.Changeset.for_create(Account, :create, %{name: input})
        result = NormalizeName.change(changeset, [], %{})

        name_value = Ash.Changeset.get_attribute(result, :name)
        actual = name_value.string

        assert actual == expected,
               "Expected '#{input}' to become '#{expected}', got '#{actual}'"
      end
    end
  end
end
