# Project Structure

## Root Directory Layout

```
qx/
├── lib/                    # Application source code
│   ├── qx/                # Core business logic (contexts)
│   └── qx_web/            # Web interface layer
├── config/                # Configuration files
├── priv/                  # Private application files
│   ├── repo/              # Database migrations and seeds
│   ├── static/            # Static assets (compiled)
│   └── gettext/           # Translation files
├── assets/                # Frontend source files
│   ├── css/               # Stylesheets
│   └── js/                # JavaScript files
├── test/                  # Test files
└── deps/                  # Dependencies (managed by Mix)
```

## Core Application Structure (`lib/qx/`)

- **`application.ex`**: OTP application supervisor tree
- **`repo.ex`**: Ecto repository for database operations
- **`mailer.ex`**: Email functionality via Swoosh
- **`cldr.ex`**: Internationalization backend
- **`secrets.ex`**: Encryption/decryption utilities
- **`accounts/`**: User authentication and account management domain

## Web Layer Structure (`lib/qx_web/`)

- **`endpoint.ex`**: Phoenix endpoint configuration
- **`router.ex`**: URL routing definitions
- **`gettext.ex`**: Translation helpers
- **`telemetry.ex`**: Application monitoring
- **`auth_overrides.ex`**: Authentication customizations
- **`live_user_auth.ex`**: LiveView authentication helpers
- **`controllers/`**: HTTP request handlers
- **`components/`**: Reusable UI components

## Configuration Files (`config/`)

- **`config.exs`**: Base application configuration
- **`dev.exs`**: Development environment settings
- **`prod.exs`**: Production environment settings
- **`runtime.exs`**: Runtime configuration
- **`test.exs`**: Test environment settings

## Asset Organization (`assets/`)

- **`css/app.css`**: Main stylesheet (Tailwind CSS)
- **`js/app.js`**: Main JavaScript entry point
- **`tsconfig.json`**: TypeScript configuration
- **`vendor/`**: Third-party assets

## Database Files (`priv/repo/`)

- **`migrations/`**: Database schema changes
- **`seeds.exs`**: Sample data for development

## Naming Conventions

### Modules
- **Contexts**: `Qx.ContextName` (e.g., `Qx.Accounts`)
- **Web modules**: `QxWeb.ModuleName` (e.g., `QxWeb.UserController`)
- **Ash resources**: `Qx.ContextName.ResourceName` (e.g., `Qx.Accounts.User`)

### Files
- **Snake_case** for file names: `user_controller.ex`, `live_user_auth.ex`
- **PascalCase** for module names: `UserController`, `LiveUserAuth`

### Ash Framework Patterns
- **Domains**: Defined in contexts (e.g., `Qx.Accounts`)
- **Resources**: Individual entities within domains
- **Actions**: CRUD operations defined on resources
- **Policies**: Authorization rules for resources

## Development Workflow

1. **Ash resources** define the core domain models and business logic
2. **Phoenix controllers/LiveViews** handle web requests and user interactions  
3. **Components** provide reusable UI elements
4. **Migrations** are generated via `mix ash_postgres.generate_migrations`
5. **Tests** mirror the `lib/` structure in `test/`

## Key Architectural Principles

- **Domain-driven design** via Ash Framework contexts
- **Separation of concerns** between business logic (`lib/qx/`) and web layer (`lib/qx_web/`)
- **Resource-based APIs** through Ash actions and policies
- **Component-based UI** with Phoenix LiveView and reusable components