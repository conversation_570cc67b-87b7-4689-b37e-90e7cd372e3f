# Product Overview

Qx is a Phoenix web application built with the Ash Framework ecosystem. It appears to be a business application with authentication, admin interfaces, and background job processing capabilities.

## Key Features

- User authentication and authorization (Ash Authentication)
- Admin interface (Ash Admin) 
- Background job processing (Oban)
- Money/financial data handling (Ash Money)
- Data encryption capabilities (Ash Cloak)
- Internationalization support (Gettext, CLDR)
- Real-time features (Phoenix LiveView)

## Target Environment

- Development server runs on `localhost:4000`
- PostgreSQL database backend
- Modern web stack with Tailwind CSS, daisyUI 5.0 and ESBuild