% Add Properties resource to Accounts domain

Summary
-------
Add a new `Property` resource to the Accounts domain. An Account can have many Properties. Both `Account.name` and `Property.name` must be normalized the same way. Include migrations, schema, context CRUD, tests, and (optionally) controller/routes.

Checklist (requirements)
- Add `Property` resource and Ecto schema.
- Model relationship: `Account` has_many `:properties`; `Property` belongs_to `:account`.
- Normalize `Property.name` the same as `Account.name` (share helper).
- Create migration for `properties` table with `account_id`, `name`, `normalized_name`, timestamps and appropriate indexes/constraints.
- Add context CRUD functions and tests for normalization, association, list, create, update, delete.
- Add tests for edge cases (whitespace, case, unicode, nil/empty, duplicates per-account if constraint enforced).

Tasks (ordered, actionable)
1) Migration
   - Create migration `priv/repo/migrations/*_create_properties.exs`.
   - Table `properties` columns: `id`, `account_id` (references `accounts`), `name` (string), `normalized_name` (string), `inserted_at`, `updated_at`.
   - Add index: `[:account_id, :normalized_name]` (document whether unique per-account is desired).

2) Schema
   - Create `lib/qx/accounts/property.ex`.
   - Define `schema "properties"` with fields `:name`, `:normalized_name` and `belongs_to :account, Qx.Accounts.Account` (match actual module path).
   - Implement `changeset/2`: validate presence of `:name` and `:account_id`, call shared normalization helper to set `:normalized_name`, add `foreign_key_constraint(:account_id)`.

3) Account schema association
   - Update `lib/qx/accounts/account.ex` (or existing Account module) to include `has_many :properties, Qx.Accounts.Property` and update any relevant preloads.

4) Context (accounts) functions
   - Add functions in the accounts context module (`lib/qx/accounts.ex` or `lib/qx/accounts/accounts.ex`):
     - `list_properties(account_or_id)` — returns properties for an account.
     - `get_property!(id)`.
     - `create_property(attrs)` — accepts `%{account_id: ..., name: ...}` and uses changeset.
     - `update_property(property, attrs)` — preserves normalization.
     - `delete_property(property)`.
   - Ensure `create_property/1` associates `account_id` and sets `normalized_name`.

5) Normalization helper
   - Extract or reuse the existing Account name normalization logic into a shared helper module (suggested path: `lib/qx/normalize.ex` or `lib/qx/accounts/normalize.ex`).
   - Use helper from both `Account` and `Property` changesets.

6) Tests (unit + context)
   - Schema tests: `test/qx/accounts/property_test.exs`
     - changeset normalizes `name` to `normalized_name`.
     - invalid without `name` or `account_id`.
   - Context tests: `test/qx/accounts/accounts_properties_test.exs` (or add to existing accounts tests)
     - `create_property/1` persists normalized name and association.
     - `list_properties/1` returns only properties for the specified account.
     - `update_property/2` re-normalizes on name changes.
     - `delete_property/1` removes record and respects foreign key constraints.
   - Edge cases to cover: leading/trailing whitespace, mixed case, unicode, empty/nil, duplicates within same account (if unique constraint enabled), creating with invalid `account_id`.

7) Controller / Routes (optional)
   - If the project exposes an API for accounts, add nested routes: `/accounts/:account_id/properties` and a controller `lib/qx_web/controllers/property_controller.ex` with JSON responses. Add controller tests under `test/qx_web/controllers`.

8) CI / Migrations
   - Add migration to repository. Run `mix ecto.migrate` in dev and ensure tests run with SQL sandbox.
   - Ensure tests run in CI and add any required migrations/setup steps to test config.

Acceptance criteria
- Migration file added and migrates without errors.
- `Property` schema compiles and passes changeset unit tests (normalization + validations).
- Accounts context functions for properties pass unit tests (create/list/update/delete behave correctly and normalization is applied).
- If controller/routes added: endpoint tests show normalized_name returned and association enforced.
- All new tests pass with `mix test`.

Suggested test cases (quick list)
- normalize: name "  AbC  " -> normalized_name "abc" (match existing Account normalization behavior).
- create rejects empty or nil name.
- create rejects missing/invalid account_id (foreign key constraint).
- list for account A returns only A's properties.
- update name re-normalizes.
- uniqueness: if unique per-account enforced, creating duplicate normalized name under same account fails.

Notes
- Match module paths (`Qx.Accounts.Account`, `Qx.Accounts.Property`, `Qx.Accounts`) to the repo's actual module layout; adjust if accounts context module is named differently.
- If Account normalization currently lives inline, extract it now so both resources use the same function.
- Keep tests small and deterministic; use SQL sandbox helpers already present in the project test setup.

If you want, I can now scaffold the migration, schema, normalization helper, and tests in the repository. Request scaffolding to proceed.
