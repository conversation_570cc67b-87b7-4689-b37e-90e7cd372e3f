---
markmap:
  colorFreezeLevel: 2
  maxWidth: 240
  initialExpandLevel: 3
---

# Qx

## Accounts

### Account

#### Fields
- name (String)

#### Relationships
- One-to-Many → Properties
- One-to-Many → Operators

### Property

#### Fields
- code (String)
- name (String)
- description (String)
- currency (String)
- logourl (String)
- facilities (Array<String>)
- rating (Float)
- bookurl (String)
- url (String)
- type (String)

#### Nested Objects
- contact (Contact)
- location (Location)
- children (Children)
- settings (Settings)
- operation (Operation)
- photos (Array<Photo>)

#### Relationships
- Belongs-to → Account
- One-to-Many → Rooms
- One-to-Many → Rates
- One-to-Many → Requests

### Operator

#### Fields
- id (String)
- fullName (String)
- nickName (String)
- title (String)
- description (String)
- phoneNumber (String)
- email (String)
- name (String)
- language (String)
- photo (Photo)

#### Relationships
- Belongs-to → Account
- One-to-Many → Quotes

### Template

#### Fields
- id (String)
- type (String)
- language (String)
- template (String)
- theme (String)
- metadata (Object)

#### Relationships
- Belongs-to → Account/Property

## Quotes

### Quote

#### Fields
- id (String)
- state (String)
- createdAt (String)
- updatedAt (String)
- markedToFollowUp (String)
- template (String)
- emailTemplate (String)
- muted (Boolean)
- language (String)

#### Nested Objects
- contact (CustomerContactInfo)
- request (RequestDetails)
- guest (GuestDetails)
- activities (Activities)
- options (RequestOptions)
- quoteOffers (Array<QuoteOffer>)
- attributes (Object)

#### Relationships
- Belongs-to → Account/Property
- Belongs-to → Operator
- One-to-Many → QuoteOffers
- May-have → Reservations (via QuoteOffers)

### QuoteOffer

#### Fields
- rateId (String)
- propertyCode (String)
- accommodationCodes (Array<String>)
- serviceCodes (Array<String>)
- title (String)
- description (String)
- checkin (String)
- nights (Int)
- rooms (Int)
- children (Int)    
- adults (Int)
- infants (Int)
- officialRate (Float)
- roomRate (Float)
- discountRate (Float)
- taxesRate (Float)
- excludedCharges (Float)
- currency (String)
- accepted (Boolean)
- paymentUrl (String)
- reservationId (String)
- boardId (Int)
- room (String)
- rate (String)
- paymentPolicyId (String)
- cancellationPolicyId (String)
- cancellationExpiration (String)

#### Nested Objects
- rate (Rate)
- accommodation (Array<Room>)
- services (Array<Service>)
- reservation (Reservation)

#### Relationships
- Belongs-to → Quote
- References → Rate
- References → Room
- References → Service
- References → Reservation

### Activities

#### Fields
- type (String)
- state (String)
- action (String)
- timestamp (String)

#### Relationships
- Belongs-to → Quote

### Template

#### Fields
- id (String)
- type (String)
- language (String)
- template (String)
- theme (String)
- metadata (Object)

#### Relationships
- Belongs-to → Account/Property

## Booking

### Reservation

#### Fields
- reservationId (String)
- status (String)
- propertyCode (String)
- accommodationCode (String)
- accommodationName (String)
- rateId (String)
- boardCode (String)
- checkin (String)
- checkout (String)
- rooms (Int)
- nights (Int)
- adults (Int)
- children (Int)
- infants (Int)
- roomRate (Float)
- currency (String)
- serviceCodes (Array<String>)
- externalId (String)

#### Nested Objects
- contact (CustomerContactInfo)
- attributes (Object)

#### Relationships
- Belongs-to → Property
- References → Room
- References → Rate

### Availability

#### Fields
- checkin (String)
- nights (Int)
- adults (Int)
- children (Int)
- infants (Int)
- rooms (Int)
- remaining (Int)
- rateId (String)
- boardId (Int)
- room (String)
- rate (String)
- accommodationCode (String)
- serviceCode (String)
- currency (String)
- paymentPolicyId (String)
- cancellationPolicyId (String)
- cancellationExpiration (String)
- officialRate (Float)
- discountRate (Float)
- roomRate (Float)
- taxesRate (Float)
- excludedCharges (Float)

#### Nested Objects
- rate (Rate)
- accommodation (Array<Room>)
- services (Array<Service>)

#### Relationships
- Belongs-to → Property
- References → Rate
- References → Room

### Service

#### Fields
- id (String)
- name (String)
- description (String)
- extra_price (Float)
- per_day (Int)
- per_adult (Int)
- per_child (Int)
- per_infant (Int)
- per_room (Int)
- required (Int)
- max_quantity (Int)
- fromd (String)
- tod (String)
- photo (String)
- excl (Array<String>)

#### Relationships
- Belongs-to → Property

### Rate

#### Fields
- id (String)
- room (String)
- name (String)
- board (Int)
- active (Int)
- public (Int)
- parent (Int)
- virtual (String)
- currency (String)
- roomName (String)
- parentRate (Int)
- fromd (String)
- tod (String)
- description (String)
- presentation (String)

#### Nested Objects
- constraints (Constraints)
- policies (Policies)

#### Relationships
- Belongs-to → Property

### Room

#### Fields
- code (String)
- name (String)
- description (String)
- amenities (Array<String>)
- active (Boolean)

#### Nested Objects
- capacity (Capacity)
- photos (Array<Photo>)

#### Relationships
- Belongs-to → Property
- One-to-Many → Rates
- Referenced-by → QuoteOffers
