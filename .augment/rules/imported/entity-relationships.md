---
markmap:
  colorFreezeLevel: 2
  maxWidth: 240
  initialExpandLevel: 2
---

# Quotelier Entity Relationships

## Core Entities

### Account
- **Primary Key**: accountName (String)
- **Fields**:
  - propertyCode (String)
  - type (String)
- **Storage**: DynamoDB AccountsPropertiesTable
- **Relationships**:
  - One-to-Many → Properties
  - One-to-Many → Operators

### Property
- **Primary Key**: code (String)
- **Fields**:
  - name (String)
  - description (String)
  - currency (String)
  - logourl (String)
  - facilities (Array<String>)
  - rating (Float)
  - bookurl (String)
  - url (String)
  - type (String)
- **Nested Objects**:
  - contact (Contact)
  - location (Location)
  - children (Children)
  - settings (Settings)
  - operation (Operation)
  - photos (Array<Photo>)
- **Storage**: S3 `{accountName}/{language}/{propertyCode}`
- **Relationships**:
  - Belongs-to → Account
  - One-to-Many → Rooms
  - One-to-Many → Rates
  - One-to-Many → Requests

### Room
- **Primary Key**: code (String)
- **Fields**:
  - name (String)
  - description (String)
  - amenities (Array<String>)
  - active (Boolean)
- **Nested Objects**:
  - capacity (Capacity)
  - photos (Array<Photo>)
- **Storage**: S3 `{accountName}/{language}/{propertyCode}/rooms/{roomCode}`
- **Relationships**:
  - Belongs-to → Property
  - One-to-Many → Rates
  - Referenced-by → Offers

### Rate
- **Primary Key**: id (String)
- **Fields**:
  - room (String)
  - name (String)
  - board (Int)
  - active (Int)
  - public (Int)
  - parent (Int)
  - virtual (String)
  - currency (String)
  - roomName (String)
  - parentRate (Int)
  - fromd (String)
  - tod (String)
  - description (String)
  - presentation (String)
- **Nested Objects**:
  - constraints (Constraints)
  - policies (Policies)
- **Storage**: S3 `{accountName}/{language}/{propertyCode}/rates/{rateId}`
- **Relationships**:
  - Belongs-to → Room
  - Belongs-to → Property
  - Referenced-by → Offers

### Request
- **Primary Key**: id (String)
- **Fields**:
  - state (String)
  - operatorId (String)
  - template (String)
  - muted (Boolean)
  - language (String)
- **Nested Objects**:
  - contact (CustomerContactInfo)
  - request (RequestDetails)
  - guest (GuestDetails)
  - activities (Activities)
  - options (RequestOptions)
  - offers (Array<Offer>)
  - attributes (Object)
- **Storage**: DynamoDB SSERequestsTable
- **Relationships**:
  - Belongs-to → Account/Property
  - Belongs-to → Operator
  - One-to-Many → Offers
  - May-have → Reservations (via Offers)

### Offer
- **Embedded in**: Request
- **Fields**:
  - rateId (String)
  - propertyCode (String)
  - accommodationCodes (Array<String>)
  - serviceCodes (Array<String>)
  - title (String)
  - description (String)
  - checkin (Date)
  - nights (Int)
  - rooms (Int)
  - children (Int)
  - adults (Int)
  - infants (Int)
  - officialRate (Float)
  - roomRate (Float)
  - discountRate (Float)
  - serviceTotalRate (Float)
  - reservationId (String)
  - taxesRate (Float)
  - excludedCharges (Float)
  - currency (String)
  - accepted (Boolean)
  - paymentUrl (String)
- **Relationships**:
  - Belongs-to → Request
  - References → Rate
  - References → Room (via accommodationCodes)
  - References → Services (via serviceCodes)
  - May-have → Reservation

### Reservation
- **Primary Key**: reservationId (String)
- **Fields**:
  - status ('active' | 'canceled')
  - propertyCode (String)
  - accommodationCode (String)
  - accommodationName (String)
  - rateId (String)
  - externalId (String)
  - boardCode (String)
  - checkin (Date)
  - checkout (Date)
  - rooms (Int)
  - nights (Int)
  - adults (Int)
  - children (Int)
  - infants (Int)
  - attributes (Object)
  - roomRate (Float)
  - currency (String)
  - serviceCodes (Array<String>)
- **Nested Objects**:
  - contact (CustomerContactInfo)
- **Storage**: External PMS + Local indexing
- **Relationships**:
  - Created-from → Offer
  - Belongs-to → Property
  - References → Rate
  - References → Room

### Operator
- **Primary Key**: id (String)
- **Fields**:
  - fullName (String)
  - nickName (String)
  - title (String)
  - description (String)
  - phoneNumber (String)
  - email (String)
  - name (String)
  - language (String)
- **Nested Objects**:
  - photo (Photo)
- **Storage**: S3 OperatorBucket
- **Relationships**:
  - Belongs-to → Account
  - One-to-Many → Requests

## Supporting Entities

### Service
- **Primary Key**: id (String)
- **Fields**:
  - name (String)
  - description (String)
  - extra_price (Float)
  - per_day (Int)
  - per_adult (Int)
  - per_child (Int)
  - per_infant (Int)
  - per_room (Int)
  - required (Int)
  - max_quantity (Int)
  - fromd (String)
  - tod (String)
  - photo (String)
  - excl (Array<String>)
- **Relationships**:
  - Belongs-to → Property
  - Referenced-by → Offers

### Contact
- **Embedded Object**
- **Fields**:
  - tel (String)
  - fax (String)
  - email (String)
  - skype (String)

### Location
- **Embedded Object**
- **Fields**:
  - lat (Float)
  - lon (Float)
  - utc_offset (Float)
  - timezone (String)
  - name (String)
  - address (String)
  - zip (String)
  - country (String)

### Photo
- **Embedded Object**
- **Fields**:
  - title (String)
  - xsmall (String)
  - small (String)
  - medium (String)
  - large (String)

### Capacity
- **Embedded Object**
- **Fields**:
  - min_pers (Int)
  - max_pers (Int)
  - max_adults (Int)
  - children_allowed (Boolean)

### Children
- **Embedded Object**
- **Fields**:
  - allowed (Int)
  - age_from (Int)
  - age_to (Int)

### Settings
- **Embedded Object**
- **Fields**:
  - nights_min (Int)
  - nights_max (Int)
  - rooms_max (Int)

### Constraints
- **Embedded Object**
- **Fields**:
  - expiration (String)
  - earlyBookLimit (Int)
  - freeCancelDays (Int)

### Policies
- **Embedded Object**
- **Fields**:
  - cancellation (String)
  - payment (String)

## Database Tables

### DynamoDB Tables
- **SSERequestsTable**: `{service}-{stage}-sse-requests`
  - Primary Key: id (String)
  - Stream: NEW_IMAGE
  - Contains: Request entities

- **AccountsPropertiesTable**: `{service}-{stage}-accounts-properties`
  - Primary Key: accountName (String)
  - Sort Key: propertyCode (String)
  - Contains: Account-Property relationships

- **OperatorTagTable**: `{service}-{stage}-tag`
  - Primary Key: operatorId (String)
  - Contains: Operator categorization

- **AttributesTable**: `{service}-{stage}-attributes-table`
  - Primary Key: id (String)
  - Sort Key: name (String)
  - Contains: Custom attributes

- **AdaptorParametersTable**: `{service}-{stage}-adaptor-parameters`
  - Primary Key: id (String)
  - Sort Key: adaptor (String)
  - Contains: Integration configuration

### S3 Buckets
- **Properties Bucket**: `{service}-{stage}-properties`
  - Structure: `{accountName}/{language}/{propertyCode}`
  - Contains: Property, Room, Rate data

- **Operators Bucket**: `{service}-{stage}-operators`
  - Contains: Operator definitions

- **Templates Bucket**: `{service}-{stage}-templates`
  - Contains: Email and document templates

- **Cache Bucket**: `{service}-{stage}-cache`
  - Contains: Integration response cache

## Key Relationships Summary

### One-to-Many Relationships
- Account → Properties
- Account → Operators
- Property → Rooms
- Property → Rates
- Property → Requests
- Room → Rates
- Request → Offers
- Operator → Requests

### Many-to-One Relationships
- Properties → Account
- Rooms → Property
- Rates → Room/Property
- Requests → Property/Account/Operator
- Offers → Request
- Reservations → Property

### Reference Relationships
- Offers → Rates (via rateId)
- Offers → Rooms (via accommodationCodes)
- Offers → Services (via serviceCodes)
- Reservations → Rates (via rateId)
- Reservations → Rooms (via accommodationCode)

### Optional Relationships
- Offers → Reservations (when accepted and booked)
- Requests → Reservations (via accepted offers)
