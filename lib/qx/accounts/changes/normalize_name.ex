defmodule Qx.Accounts.Changes.NormalizeName do
  @moduledoc """
  A change that normalizes the name attribute by:
  - Trimming whitespace
  - Converting spaces to dashes
  - Converting to uppercase

  This ensures names are always single strings of characters without spaces.
  Handles both regular strings and Ash.CiString values.
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    case Ash.Changeset.get_attribute(changeset, :name) do
      nil ->
        changeset

      %Ash.CiString{string: name_string} ->
        normalized_name = normalize_name(name_string)
        Ash.Changeset.force_change_attribute(changeset, :name, normalized_name)

      name when is_binary(name) ->
        normalized_name = normalize_name(name)
        Ash.Changeset.force_change_attribute(changeset, :name, normalized_name)

      _ ->
        changeset
    end
  end

  defp normalize_name(name) when is_binary(name) do
    name
    |> String.trim()
    |> String.replace(~r/\s+/, "-")
    |> String.upcase()
  end
end
