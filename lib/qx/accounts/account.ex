defmodule Qx.Accounts.Account do
  use Ash.Resource,
    otp_app: :qx,
    domain: Qx.Accounts,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer]

  postgres do
    table "accounts"
    repo Qx.Repo
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      primary? true
      accept [:name, :description, :status]
    end

    update :update do
      primary? true
      accept [:name, :description, :status]
      require_atomic? false
    end

    read :list_active do
      filter expr(status == :active)
    end

    update :activate do
      accept []
      change set_attribute(:status, :active)
      require_atomic? false
    end

    update :deactivate do
      accept []
      change set_attribute(:status, :inactive)
      require_atomic? false
    end
  end

  policies do
    bypass AshAuthentication.Checks.AshAuthenticationInteraction do
      authorize_if always()
    end

    policy always() do
      authorize_if always()
    end
  end

  changes do
    change Qx.Accounts.Changes.NormalizeName
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :ci_string do
      allow_nil? false
      public? true
    end

    attribute :description, :string do
      public? true
    end

    attribute :status, :atom do
      constraints one_of: [:active, :inactive, :suspended]
      default :active
      public? true
    end

    timestamps()
  end

  relationships do
    has_many :users, Qx.Accounts.User
  end
end
