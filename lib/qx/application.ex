defmodule Qx.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      QxWeb.Telemetry,
      Qx.Repo,
      {DNSCluster, query: Application.get_env(:qx, :dns_cluster_query) || :ignore},
      {Oban,
       AshOban.config(
         Application.fetch_env!(:qx, :ash_domains),
         Application.fetch_env!(:qx, <PERSON>ban)
       )},
      {Phoenix.PubSub, name: Qx.PubSub},
      # Start a worker by calling: Qx.Worker.start_link(arg)
      # {Qx.Worker, arg},
      # Start to serve requests, typically the last entry
      QxWeb.Endpoint,
      {AshAuthentication.Supervisor, [otp_app: :qx]}
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Qx.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    QxWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
